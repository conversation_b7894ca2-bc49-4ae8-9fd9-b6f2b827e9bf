# 環境配置
APP_ENVIRONMENT=sandbox

# PayUni 支付設定 - 測試環境範例
PAYUNI_SANDBOX_MER_ID=S01421169
PAYUNI_SANDBOX_HASH_KEY=********************************
PAYUNI_SANDBOX_HASH_IV=****************

# PayUni 支付設定 - 正式環境範例
PAYUNI_PRODUCTION_MER_ID=SHOP_EXAMPLE_ID
PAYUNI_PRODUCTION_HASH_KEY=EXAMPLE_PRODUCTION_HASH_KEY
PAYUNI_PRODUCTION_HASH_IV=EXAMPLE_PRODUCTION_IV

# URL 設定
NEXT_PUBLIC_BASE_URL=https://test.example.com
PAYUNI_NOTIFY_URL=https://test.example.com/api/webhook/payment

# Google Sheets 設定
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\nTEST_PRIVATE_KEY_CONTENT\n-----END PRIVATE KEY-----\n

# Google Sheets - 測試環境
GOOGLE_SANDBOX_SHEET_ID=TEST_SHEET_ID
GOOGLE_SANDBOX_WATCH_SHEET_ID=TEST_WATCH_SHEET_ID
GOOGLE_SANDBOX_BLOG_SHEET_ID=TEST_BLOG_SHEET_ID
GOOGLE_SANDBOX_CONTACT_SHEET_ID=TEST_CONTACT_SHEET_ID
GOOGLE_SANDBOX_WATCH_APPOINTMENT_SHEET_ID=TEST_WATCH_APPOINTMENT_SHEET_ID
GOOGLE_SANDBOX_PANGEA_APPOINTMENT_SHEET_ID=TEST_PANGEA_APPOINTMENT_SHEET_ID 

# Google Sheets - 正式環境
GOOGLE_PRODUCTION_SHEET_ID=PROD_SHEET_ID
GOOGLE_PRODUCTION_WATCH_SHEET_ID=PROD_WATCH_SHEET_ID
GOOGLE_PRODUCTION_BLOG_SHEET_ID=PROD_BLOG_SHEET_ID
GOOGLE_PRODUCTION_CONTACT_SHEET_ID=PROD_CONTACT_SHEET_ID
GOOGLE_PRODUCTION_WATCH_APPOINTMENT_SHEET_ID=PROD_WATCH_APPOINTMENT_SHEET_ID
GOOGLE_PRODUCTION_PANGEA_APPOINTMENT_SHEET_ID=PROD_PANGEA_APPOINTMENT_SHEET_ID

# Meta Pixel - 測試環境
META_SANDBOX_PIXEL_ID=****************
META_SANDBOX_ACCESS_TOKEN=TEST_ACCESS_TOKEN
META_SANDBOX_TEST_EVENT_CODE=TEST_EVENT_CODE

# Meta Pixel - 正式環境
META_PRODUCTION_PIXEL_ID=PROD_PIXEL_ID
META_PRODUCTION_ACCESS_TOKEN=PROD_ACCESS_TOKEN
META_PRODUCTION_TEST_EVENT_CODE=PROD_TEST_CODE

# GTM - 測試環境
NEXT_PUBLIC_GTM_SANDBOX_ID=GTM-TEST123
NEXT_PUBLIC_GTM_SANDBOX_PROXY_DOMAIN=gtm.test.example.com

# GTM - 正式環境
NEXT_PUBLIC_GTM_PRODUCTION_ID=GTM-PROD123
NEXT_PUBLIC_GTM_PRODUCTION_PROXY_DOMAIN=gtm.example.com

# CAPI 控制開關
NEXT_PUBLIC_CAPI_ENABLED=false

# 快取系統設定（可選，有合理預設值）
# CACHE_ENABLED=true          # 是否啟用快取（預設：true）
# CACHE_TTL=300000            # 通用快取 TTL（預設：5分鐘，各區塊有專用設定）
# CACHE_MAX_SIZE=50           # 最大快取項目數（預設：50）
# CACHE_LOGGING=false         # 生產環境建議關閉（開發環境自動啟用）

# 注意：快取預熱已內建，以下變數無效
# CACHE_AUTO_WARMUP=true      ❌ 程式碼中未使用
# CACHE_WARMUP_INTERVAL=1800000  ❌ 程式碼中未使用
# CACHE_PERIODIC_WARMUP=false    ❌ 程式碼中未使用
