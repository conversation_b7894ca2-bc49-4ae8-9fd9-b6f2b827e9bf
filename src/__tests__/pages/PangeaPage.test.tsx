/**
 * Pangea 頁面組件測試
 * 測試 Pangea 頁面的渲染和基本互動功能
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import PangeaPage from '@/app/pangea/page';

// Mock Next.js components
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: React.ComponentProps<'img'>) {
    return <img src={src} alt={alt} {...props} />;
  };
});

jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) {
    return <a href={href} {...props}>{children}</a>;
  };
});

// Mock AutoCarousel component
jest.mock('@/components/AutoCarousel', () => {
  return function MockAutoCarousel({ images }: { images: Array<{ src: string; alt: string }> }) {
    return (
      <div data-testid="auto-carousel">
        {images.map((image: { src: string; alt: string }, index: number) => (
          <img key={index} src={image.src} alt={image.alt} />
        ))}
      </div>
    );
  };
});

describe('PangeaPage', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('基本渲染測試', () => {
    test('應該正確渲染首頁標題', () => {
      render(<PangeaPage />);

      // 檢查主要標題
      expect(screen.getByText('全球第一台照顧機芯的錶盒')).toBeInTheDocument();
      expect(screen.getAllByText('PANGEA 機械錶智慧收藏盒').length).toBeGreaterThanOrEqual(2); // 頁面中有多個相同標題
    });

    test('應該顯示主要導航連結', () => {
      render(<PangeaPage />);

      // 檢查主要 CTA 按鈕 - 使用 getAllByText 因為有多個相同按鈕
      const ctaButtons = screen.getAllByText('立即預約體驗');
      expect(ctaButtons.length).toBeGreaterThan(0);
    });

    test('應該顯示主要 CTA 按鈕', () => {
      render(<PangeaPage />);

      // 檢查主要按鈕 - 使用 getAllByText 並檢查第一個
      const ctaButtons = screen.getAllByText('立即預約體驗');
      expect(ctaButtons[0]).toBeInTheDocument();
      expect(ctaButtons[0].closest('a')).toHaveAttribute('href', '/pangea-booking');
    });

    test('應該顯示服務特色區塊', () => {
      render(<PangeaPage />);

      // 檢查主要區塊標題
      expect(screen.getByText('你的專屬錶匠')).toBeInTheDocument();
      expect(screen.getByText('匠心獨具 | 三大功能')).toBeInTheDocument();
    });
  });

  describe('響應式設計測試', () => {
    test('應該在桌面版正確顯示', () => {
      // 模擬桌面螢幕尺寸
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });

      render(<PangeaPage />);

      // 檢查桌面版特有的元素
      const mainSection = screen.getByRole('main');
      expect(mainSection).toBeInTheDocument();
    });

    test('應該在行動版正確顯示', () => {
      // 模擬行動裝置螢幕尺寸
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<PangeaPage />);

      // 檢查行動版的響應式元素
      const mainSection = screen.getByRole('main');
      expect(mainSection).toBeInTheDocument();
    });
  });

  describe('互動功能測試', () => {
    test('應該正確處理 CTA 按鈕點擊', () => {
      render(<PangeaPage />);

      // 測試主要 CTA 按鈕 - 使用 getAllByText 因為有多個相同按鈕
      const ctaButtons = screen.getAllByText('立即預約體驗');
      fireEvent.click(ctaButtons[0]);

      // 驗證連結正確
      expect(ctaButtons[0].closest('a')).toHaveAttribute('href', '/pangea-booking');
    });

    test('應該正確處理滾動功能', async () => {
      // Mock scrollIntoView
      const mockScrollIntoView = jest.fn();
      Element.prototype.scrollIntoView = mockScrollIntoView;

      render(<PangeaPage />);

      // 檢查頁面渲染正常
      expect(screen.getByText('全球第一台照顧機芯的錶盒')).toBeInTheDocument();
    });
  });

  describe('內容區塊測試', () => {
    test('應該顯示 Hero 區塊', () => {
      render(<PangeaPage />);

      // 檢查 Hero 區塊的關鍵元素
      expect(screen.getByText('全球第一台照顧機芯的錶盒')).toBeInTheDocument();
      expect(screen.getAllByText('PANGEA 機械錶智慧收藏盒').length).toBeGreaterThanOrEqual(2);

      // 檢查主要區塊
      const heroSection = screen.getByRole('main');
      expect(heroSection).toBeInTheDocument();
    });

    test('應該顯示服務介紹區塊', () => {
      render(<PangeaPage />);

      // 檢查服務介紹的標題和內容
      expect(screen.getByText('你的專屬錶匠')).toBeInTheDocument();
      expect(screen.getByText(/我們和 30 年經驗老師傅合作打造/)).toBeInTheDocument();
    });

    test('應該顯示特色服務區塊', () => {
      render(<PangeaPage />);

      // 檢查特色服務內容
      expect(screen.getByText(/掌握愛錶每一刻變化/)).toBeInTheDocument();
      expect(screen.getByText(/時時刻刻照顧愛錶/)).toBeInTheDocument();
      expect(screen.getByText(/讓享受機械錶變簡單/)).toBeInTheDocument();
    });

    test('應該顯示精選錶款區塊', () => {
      render(<PangeaPage />);

      // 檢查主要內容存在
      expect(screen.getByText('全球第一台照顧機芯的錶盒')).toBeInTheDocument();

      // 檢查是否有圖片元素
      const images = screen.getAllByRole('img');
      expect(images.length).toBeGreaterThan(0);
    });

    test('應該顯示客戶見證區塊', () => {
      render(<PangeaPage />);

      // 檢查主要內容
      expect(screen.getByText(/在數位盤古大陸 PANGEA 上留下屬於你的足跡/)).toBeInTheDocument();

      // 檢查 CTA 按鈕
      const ctaButtons = screen.getAllByText('立即預約體驗');
      expect(ctaButtons.length).toBeGreaterThan(0);
    });
  });

  describe('SEO 和可訪問性測試', () => {
    test('應該有正確的頁面標題', () => {
      render(<PangeaPage />);

      // 檢查主要標題的語義標籤 - 使用 getAllByRole 因為有多個 h1
      const mainHeadings = screen.getAllByRole('heading', { level: 1 });
      expect(mainHeadings.length).toBeGreaterThan(0);
    });

    test('應該有正確的圖片 alt 屬性', () => {
      render(<PangeaPage />);
      
      // 檢查所有圖片都有 alt 屬性
      const images = screen.getAllByRole('img');
      images.forEach(img => {
        expect(img).toHaveAttribute('alt');
        expect(img.getAttribute('alt')).not.toBe('');
      });
    });

    test('應該有正確的連結可訪問性', () => {
      render(<PangeaPage />);
      
      // 檢查所有連結都有可訪問的文字
      const links = screen.getAllByRole('link');
      links.forEach(link => {
        expect(link).toHaveAccessibleName();
      });
    });

    test('應該有正確的按鈕可訪問性', () => {
      render(<PangeaPage />);
      
      // 檢查所有按鈕都有可訪問的文字
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAccessibleName();
      });
    });
  });

  describe('錯誤處理測試', () => {
    test('應該優雅處理圖片載入失敗', () => {
      render(<PangeaPage />);

      // 模擬圖片載入失敗
      const images = screen.getAllByRole('img');
      images.forEach(img => {
        fireEvent.error(img);
      });

      // 頁面應該仍然可用
      expect(screen.getByText('全球第一台照顧機芯的錶盒')).toBeInTheDocument();
    });

    test('應該優雅處理組件載入失敗', () => {
      // 這個測試確保即使某些組件失敗，頁面仍然可用
      render(<PangeaPage />);

      // 檢查核心內容仍然存在
      expect(screen.getByText('全球第一台照顧機芯的錶盒')).toBeInTheDocument();
      expect(screen.getAllByText('PANGEA 機械錶智慧收藏盒').length).toBeGreaterThanOrEqual(2);
    });
  });
});
