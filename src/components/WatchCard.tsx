import Link from 'next/link';
import LazyImage from './LazyImage';
import { WatchListItem } from '@/types/watch';

interface WatchCardProps {
  watch: WatchListItem;
}

const WatchCard = ({ watch }: WatchCardProps) => {
  // 格式化價格顯示
  const formatPrice = (price: number) => {
    return `NT$ ${new Intl.NumberFormat('zh-TW').format(price)}`;
  };

  return (
    <Link href={`/pre-owned/${watch.seoSlug || watch.id}`} className="block h-full w-full">
      <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group border border-slate-200 watch-card h-full w-full flex flex-col">
        {/* 手錶圖片 */}
        <div className="relative w-full h-64 overflow-hidden flex-shrink-0">
          <LazyImage
            src={watch.thumbnail || '/images/placeholder-watch.svg'}
            alt={watch.productName}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            placeholder="/images/placeholder-watch.svg"
          />
        </div>

        {/* 手錶資訊 */}
        <div className="p-6 flex flex-col flex-grow min-w-0">
          {/* 品牌 */}
          <p className="text-sm font-medium uppercase tracking-wide mb-2 truncate" style={{ color: '#2b354d', opacity: 0.7 }}>
            {watch.brand}
          </p>

          {/* 產品名稱 */}
          <h3 className="text-lg font-semibold mb-3 line-clamp-2 leading-tight flex-grow min-w-0 break-words" style={{ color: '#2b354d' }}>
            {watch.productName}
          </h3>

          {/* 價格 */}
          <p className="text-xl font-bold mt-auto truncate" style={{ color: '#2b354d' }}>
            {formatPrice(watch.price)}
          </p>
        </div>
      </div>
    </Link>
  );
};

export default WatchCard;
