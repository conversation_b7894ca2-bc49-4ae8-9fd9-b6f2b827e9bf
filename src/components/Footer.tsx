"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { cn } from '@/lib/utils';

const Footer = () => {
  return (
    <footer className="bg-white border-t border-gray-200 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content - Left Right Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 mb-6">
          {/* 左側：公司資訊 */}
          <div>
            {/* Logo */}
            <Link href="/" className="inline-block mb-4 hover:opacity-80 transition-opacity">
              <Image
                src="/images/logo.png"
                alt="Weaven Logo"
                width={120}
                height={40}
                className="h-8 w-auto"
              />
            </Link>

            {/* 聯絡資訊 */}
            <div className="space-y-2 text-sm text-gray-600">
              <p>新北市三重區重新路一段 108 號 3F</p>
              <p>
                <a
                  href="mailto:<EMAIL>"
                  className="hover:text-gray-900 transition-colors"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>

          {/* 右側：導航連結 - 三欄緊湊佈局 */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            {/* 關於 Weaven */}
            <div>
              <h3 className="text-sm font-semibold mb-3" style={{ color: '#2b354d' }}>
                關於 Weaven
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/about"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    品牌故事
                  </Link>
                </li>
                <li>
                  <Link
                    href="/blog"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    腕錶文章
                  </Link>
                </li>
                <li>
                  <Link
                    href="/contact"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    聯絡我們
                  </Link>
                </li>
              </ul>
            </div>

            {/* 產品與服務 */}
            <div>
              <h3 className="text-sm font-semibold mb-3" style={{ color: '#2b354d' }}>
                產品與服務
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/pangea-booking"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    Pangea 預約
                  </Link>
                </li>
                <li>
                  <Link
                    href="/pre-owned-watches"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    典藏錶款
                  </Link>
                </li>
                <li>
                  <Link
                    href="/events"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    錶匠體驗
                  </Link>
                </li>
                <li>
                  <a
                    href="https://aquapure.weaven.co"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors inline-flex items-center gap-1"
                  >
                    AQUA PURE
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </li>
              </ul>
            </div>

            {/* 顧客支援 */}
            <div>
              <h3 className="text-sm font-semibold mb-3" style={{ color: '#2b354d' }}>
                顧客支援
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/support"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    常見問題
                  </Link>
                </li>
                <li>
                  <Link
                    href="/support/pangea-warranty"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    保固條款
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer Bottom - Social Media, Copyright, Privacy Policy */}
        <div className="border-t border-gray-200 pt-4">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
            {/* Left side - Social Media Links */}
            <div className="flex items-center space-x-3">
              <Link
                href="https://www.facebook.com/weaven.co"
                target="_blank"
                rel="noopener noreferrer"
                className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200",
                  "bg-gray-100 hover:bg-gray-200 hover:scale-105",
                  "border border-gray-200 hover:border-gray-300"
                )}
                style={{
                  color: '#2b354d'
                }}
                aria-label="Facebook"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </Link>
              <Link
                href="https://www.instagram.com/weaven2019"
                target="_blank"
                rel="noopener noreferrer"
                className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200",
                  "bg-gray-100 hover:bg-gray-200 hover:scale-105",
                  "border border-gray-200 hover:border-gray-300"
                )}
                style={{
                  color: '#2b354d'
                }}
                aria-label="Instagram"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </Link>
            </div>

            {/* Right side - Copyright and Privacy Policy */}
            <div className="flex flex-col sm:flex-row items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs text-gray-600">
              <p>Copyright © 2025 Weaven.co. All rights reserved.</p>
              <Link
                href="/privacy-policy"
                className="hover:text-gray-900 transition-colors"
              >
                隱私權政策
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;