@import "tailwindcss";

:root {
  /* Pangea Custom Color Scheme */
  --background: #ffffff;
  --foreground: #2b354d;

  /* Primary colors - Deep blue-gray */
  --primary: #2b354d;
  --primary-foreground: #ffffff;

  /* Secondary colors */
  --secondary: #f8fafc;
  --secondary-foreground: #2b354d;

  /* Accent colors - Subtle blue for emphasis */
  --accent: #3b82f6;
  --accent-foreground: #ffffff;

  /* Muted colors */
  --muted: #f1f5f9;
  --muted-foreground: #64748b;

  /* Border and input */
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;

  /* Card */
  --card: #ffffff;
  --card-foreground: #2b354d;

  /* Popover */
  --popover: #ffffff;
  --popover-foreground: #2b354d;

  /* Destructive */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;

  /* Chart colors */
  --chart-1: #3b82f6;
  --chart-2: #6366f1;
  --chart-3: #2b354d;
  --chart-4: #64748b;
  --chart-5: #94a3b8;

  /* Additional section colors for variety */
  --section-light: #f8fafc;
  --section-neutral: #f1f5f9;
  --section-accent: #eff6ff;
}

.dark {
  --background: #0f172a;
  --foreground: #f8fafc;

  --primary: #2b354d;
  --primary-foreground: #ffffff;

  --secondary: #1e293b;
  --secondary-foreground: #f8fafc;

  --accent: #3b82f6;
  --accent-foreground: #0f172a;

  --muted: #1e293b;
  --muted-foreground: #94a3b8;

  --border: #334155;
  --input: #334155;
  --ring: #3b82f6;

  --card: #1e293b;
  --card-foreground: #f8fafc;

  --popover: #1e293b;
  --popover-foreground: #f8fafc;

  --destructive: #ef4444;
  --destructive-foreground: #ffffff;

  --chart-1: #3b82f6;
  --chart-2: #6366f1;
  --chart-3: #64748b;
  --chart-4: #94a3b8;
  --chart-5: #cbd5e1;

  /* Additional section colors for variety */
  --section-light: #1e293b;
  --section-neutral: #334155;
  --section-accent: #1e3a8a;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Shimmer animation for skeleton loading */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Custom prose styles for blog content */
.prose {
  color: #2b354d;
  max-width: 65ch;
}

.prose h1 {
  color: #2b354d;
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose h2 {
  color: #2b354d;
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose h3 {
  color: #2b354d;
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose h4 {
  color: #2b354d;
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  line-height: 1.75;
}

.prose a {
  color: #2b354d;
  text-decoration: underline;
  font-weight: 500;
}

.prose a:hover {
  color: #2b354d;
  opacity: 0.8;
}

.prose strong {
  color: #2b354d;
  font-weight: 600;
}

.prose ul {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose ol {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  line-height: 1.75;
}

.prose li::marker {
  color: #6b7280;
}

.prose blockquote {
  font-weight: 500;
  font-style: italic;
  color: #111827;
  border-left-width: 0.25rem;
  border-left-color: #e5e7eb;
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-left: 1em;
}

.prose code {
  color: #111827;
  font-weight: 600;
  font-size: 0.875em;
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.prose pre {
  color: #e5e7eb;
  background-color: #1f2937;
  overflow-x: auto;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding: 1rem;
}

.prose img {
  margin-top: 2em;
  margin-bottom: 2em;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.prose hr {
  border-color: #e5e7eb;
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}

.prose table {
  width: 100%;
  table-layout: auto;
  text-align: left;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
  border-collapse: collapse;
}

.prose th {
  color: #111827;
  font-weight: 600;
  vertical-align: bottom;
  padding: 0.5714286em;
  border-bottom-width: 1px;
  border-bottom-color: #d1d5db;
}

.prose td {
  vertical-align: baseline;
  padding: 0.5714286em;
  border-bottom-width: 1px;
  border-bottom-color: #e5e7eb;
}

/* Responsive prose styles */
.prose-lg {
  font-size: 1.125em;
  line-height: 1.7777778;
}

.prose-lg h1 {
  font-size: 2.6666667em;
  margin-top: 0;
  margin-bottom: 0.8333333em;
  line-height: 1;
}

.prose-lg h2 {
  font-size: 1.6666667em;
  margin-top: 1.8666667em;
  margin-bottom: 1.0666667em;
  line-height: 1.3333333;
}

.prose-lg h3 {
  font-size: 1.3333333em;
  margin-top: 1.6666667em;
  margin-bottom: 0.6666667em;
  line-height: 1.5;
}

.prose-lg p {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}

.prose-lg ul, .prose-lg ol {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}

.prose-lg li {
  margin-top: 0.6666667em;
  margin-bottom: 0.6666667em;
}

/* Max width utilities */
.max-w-none {
  max-width: none;
}

/* Watch description styles */
.watch-description strong {
  display: block;
  margin-bottom: 4px;
  font-weight: 600;
  line-height: 1.5;
}

.watch-description a {
  color: #2b354d;
  text-decoration: underline;
  word-break: break-all;
}

.watch-description a:hover {
  opacity: 0.8;
}

/* iOS Safari 兼容性修復 - 加強版 */
.ios-safari,
.ios-safari-detected {
  /* 修復 iOS Safari 上的滾動問題 */
  -webkit-overflow-scrolling: touch;
  /* 防止 iOS Safari 的自動縮放和布局偏移 */
  -webkit-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
  /* 修復 iOS Safari 的 viewport 計算問題 */
  min-height: 100vh;
  min-height: -webkit-fill-available;
  /* 強制禁用硬體加速可能導致的問題 */
  -webkit-perspective: none !important;
  perspective: none !important;
}



/* 修復 iOS Safari 的 body 高度計算 */
.ios-safari body {
  min-height: 100vh;
  min-height: -webkit-fill-available;
}

/* 修復 iOS Safari 的主容器高度 */
.ios-safari .flex.min-h-screen {
  min-height: 100vh;
  min-height: -webkit-fill-available;
}

/* 修復 iOS Safari 上的 sticky 定位問題 - 特別針對 Header */
.ios-safari header.sticky {
  position: -webkit-sticky;
  position: sticky;
  /* 確保在 iOS Safari 上正確的層級和定位 */
  z-index: 9999;
  top: 0;
  /* 強制硬體加速以避免渲染問題 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* 確保背景完全覆蓋 */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* 調整 iOS Safari 動畫降級策略 - 不完全禁用 transform */
.ios-safari .motion-safe {
  /* 在 iOS Safari 上簡化動畫，但保留基本的 transform */
  transition-duration: 0.2s !important;
  /* 移除 transform: none，改為保留基本的 translateZ */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* iOS Safari 表單元素 transition 修復 */
.ios-safari .movement-assembling-form input,
.ios-safari .movement-assembling-form select,
.ios-safari .movement-assembling-form textarea,
.ios-safari .movement-assembling-form label,
.ios-safari .movement-assembling-form button {
  /* 禁用可能干擾表單元素顯示的 transition */
  transition: none !important;
  -webkit-transition: none !important;
  /* 確保表單元素在 iOS Safari 上正確顯示 */
  -webkit-appearance: none;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* iOS Safari 表單選項特殊修復 */
.ios-safari .movement-assembling-form input[type="checkbox"],
.ios-safari .movement-assembling-form input[type="radio"] {
  /* 強制顯示表單控件 */
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  position: relative !important;
  /* 確保在 iOS Safari 上可以點擊 */
  pointer-events: auto !important;
  /* 移除可能干擾的樣式 */
  -webkit-appearance: none;
  appearance: none;
  /* 添加基本樣式以確保可見 */
  width: 16px !important;
  height: 16px !important;
  border: 2px solid #2b354d !important;
  border-radius: 4px !important;
  background-color: white !important;
}

/* iOS Safari 選中狀態修復 */
.ios-safari .movement-assembling-form input[type="checkbox"]:checked,
.ios-safari .movement-assembling-form input[type="radio"]:checked {
  background-color: #2b354d !important;
  border-color: #2b354d !important;
}

/* iOS Safari 複選框勾選標記 */
.ios-safari .movement-assembling-form input[type="checkbox"]:checked::after {
  content: '✓' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: white !important;
  font-size: 12px !important;
  font-weight: bold !important;
  line-height: 1 !important;
}

/* iOS Safari 單選框選中標記 */
.ios-safari .movement-assembling-form input[type="radio"]:checked::after {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 8px !important;
  height: 8px !important;
  background-color: white !important;
  border-radius: 50% !important;
}

/* iOS Safari 單選框圓形樣式 */
.ios-safari .movement-assembling-form input[type="radio"] {
  border-radius: 50% !important;
}

.ios-safari img {
  /* 修復 iOS Safari 上的圖片顯示問題 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.ios-safari .will-change-transform {
  /* 在 iOS Safari 上避免使用 will-change */
  will-change: auto !important;
}

/* 修復 iOS Safari 上的 grid 佈局問題 */
.ios-safari .grid {
  display: -webkit-grid;
  display: grid;
}

/* 修復 iOS Safari 上的 object-fit 問題 */
.ios-safari .object-cover {
  object-fit: cover;
}

.ios-safari .object-contain {
  object-fit: contain;
}

/* 修復 iOS Safari 上的 backdrop-filter 問題 */
.ios-safari .backdrop-blur {
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

/* 通用 sticky 定位修復（非 header 元素） */
.ios-safari .sticky:not(header) {
  position: -webkit-sticky;
  position: sticky;
}

/* 修復 iOS Safari 上的 appearance 問題 */
.ios-safari input,
.ios-safari textarea,
.ios-safari select {
  -webkit-appearance: none;
  appearance: none;
}

/* 修復 iOS Safari 上的 touch 事件問題 */
.ios-safari button,
.ios-safari [role="button"] {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}



/* 修復 iOS Safari 上的標題偏移問題 - 加強版 */
.ios-safari h1,
.ios-safari h2,
.ios-safari h3,
.ios-safari h4,
.ios-safari h5,
.ios-safari h6,
.ios-safari-detected h1,
.ios-safari-detected h2,
.ios-safari-detected h3,
.ios-safari-detected h4,
.ios-safari-detected h5,
.ios-safari-detected h6 {
  /* 確保標題在 iOS Safari 上正確渲染 */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  /* 防止字體渲染偏移 */
  text-rendering: optimizeLegibility !important;
  /* 完全禁用 transform 相關屬性 */
  -webkit-transform: none !important;
  transform: none !important;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  /* 強制使用靜態定位 */
  position: static !important;
  /* 重置左右 margin，保留上下間距 */
  margin-left: 0 !important;
  margin-right: 0 !important;
  /* 確保在 iOS Safari 上的正確對齊 */
  text-align: inherit !important;
  /* 禁用硬體加速 */
  will-change: auto !important;
  /* 確保正確的層疊上下文 */
  z-index: auto !important;
  /* 新增：防止容器寬度問題 */
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  /* 新增：確保文字不會溢出 */
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}





/* iPad Pro 特定優化 */
@media screen and (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
  /* iPad Pro 橫向模式優化 */
  .max-w-7xl {
    max-width: 1200px;
  }

  /* 圖片區塊置中優化 */
  section .grid {
    align-items: center;
    justify-items: center;
  }

  /* 圖片容器優化 - 排除 DesignGallerySection */
  .flex.gap-4:not(.design-gallery-section .flex) {
    max-width: 500px;
    margin: 0 auto;
  }
}

/* 1024px-1366px 區間的容器和佈局優化 */
@media screen and (min-width: 1024px) and (max-width: 1366px) {
  /* 擴展容器最大寬度以更好利用空間 */
  .max-w-6xl {
    max-width: 1200px !important;
  }

  /* 增加容器的 padding 以更好地利用空間 */
  .max-w-6xl {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }

  /* 只針對手錶類型選項的佈局優化 - 精確選擇器 */
  .grid[class*="grid-cols-2"][class*="md:grid-cols-5"] {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
  }

  /* 針對首頁活動費用 & 活動地點區塊 - 保持 2 列佈局但增加間距 */
  .design-gallery-section + section .grid[class*="md:grid-cols-2"]:not([class*="lg:grid-cols"]):not([class*="md:grid-cols-5"]) {
    gap: 3rem !important;
  }

  /* 確保活動介紹區塊保持原有佈局 */
  section .grid[class*="lg:grid-cols-3"],
  section .grid[class*="lg:grid-cols-4"] {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  /* 錶匠體驗頁面特定優化 */
  /* 確保活動費用和活動地點區塊在 grid 容器內左對齊 */
  .movement-assembling-cost-section,
  .movement-assembling-location-section {
    justify-self: start !important;
    align-self: start !important;
    text-align: left !important;
    width: 100% !important;
  }

  /* 確保活動費用和活動地點區塊的標題和內容都左對齊 */
  .movement-assembling-cost-section .flex.items-center,
  .movement-assembling-location-section .flex.items-center,
  .movement-assembling-cost-section .space-y-3,
  .movement-assembling-location-section .space-y-3 {
    justify-content: flex-start !important;
    text-align: left !important;
  }

  /* 錶匠體驗頁面表單選項寬度優化 - 保持原有列數但撐開選項 */

  /* 場次時間和參加方式 - 保持 2 列但撐開 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="lg:grid-cols-2"] {
    gap: 1rem !important;
    max-width: none !important;
  }

  /* 個人資訊：姓名 & Email - 2 列佈局撐開並對齊 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="md:grid-cols-2"]:not([class*="lg:"]) {
    gap: 1.5rem !important;
    max-width: none !important;
    align-items: start !important;
  }

  /* 確保姓名和 Email 輸入欄位高度一致 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="md:grid-cols-2"]:not([class*="lg:"]) input {
    height: 48px !important;
    padding: 12px 16px !important;
  }

  /* 基本資訊：性別、年齡、居住地區 - 3 列佈局撐開 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="md:grid-cols-3"] {
    gap: 1.5rem !important;
    max-width: none !important;
  }

  /* 手錶類型選項 - 保持 5 列但撐開 */
  .movement-assembling-form .grid[class*="grid-cols-2"][class*="lg:grid-cols-5"] {
    gap: 0.75rem !important;
    max-width: none !important;
  }

  /* 性別選項 - 水平排列時集中在左側 */
  .movement-assembling-form [class*="space-y-2"][class*="md:space-y-0"][class*="md:flex"][class*="md:gap-6"] {
    gap: 1.5rem !important;
    justify-content: flex-start !important;
    /* 移除最大寬度限制，讓選項有足夠空間 */
    max-width: none !important;
    width: 100% !important;
  }

  /* 所有表單選項標籤撐開填滿 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="lg:grid-cols-2"] label,
  .movement-assembling-form .grid[class*="grid-cols-2"][class*="lg:grid-cols-5"] label {
    width: 100% !important;
    min-width: 0 !important;
    flex: 1 !important;
    padding: 1rem 1.25rem !important;
    min-height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    text-align: left !important;
  }

  /* 參加方式選項特殊高度 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="lg:grid-cols-2"] label[class*="min-h-"] {
    min-height: 70px !important;
    padding: 1.25rem !important;
  }

  /* 所有表單輸入欄位優化 - 包含個人資訊和基本資訊 */
  .movement-assembling-form input[type="text"],
  .movement-assembling-form input[type="email"],
  .movement-assembling-form input[type="tel"],
  .movement-assembling-form select,
  .movement-assembling-form textarea {
    width: 100% !important;
    max-width: none !important;
  }

  /* 個人資訊區塊的輸入欄位容器撐開 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="md:grid-cols-2"] > div,
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="md:grid-cols-3"] > div {
    width: 100% !important;
    min-width: 0 !important;
  }

  /* 性別選項的 flex 容器優化 - 不撐開，保持自然寬度 */
  .movement-assembling-form [class*="space-y-2"][class*="md:space-y-0"][class*="md:flex"][class*="md:gap-6"] label {
    flex: none !important;
    justify-content: flex-start !important;
    min-width: auto !important;
    white-space: nowrap !important;
  }

  /* 確保容器充分利用空間 */
  .movement-assembling-form .space-y-6,
  .movement-assembling-form .space-y-8 {
    width: 100% !important;
    max-width: none !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  /* iPad Pro 直向模式優化 */

  /* DesignGallerySection 右側小圖容器優化 */
  .design-gallery-section .flex.flex-col {
    /* 確保右側小圖在 iPad 上垂直平均分佈 */
    justify-content: space-between !important;
    /* 讓容器自然拉伸，不強制高度 */
    align-self: stretch !important;
  }

  /* 確保外層容器正確對齊 */
  .design-gallery-section .flex:not(.flex-col) {
    align-items: stretch !important;
  }

  /* 圖片區塊置中 - 排除 DesignGallerySection */
  .flex.gap-3:not(.design-gallery-section .flex),
  .flex.gap-4:not(.design-gallery-section .flex) {
    justify-content: center;
    max-width: 600px;
    margin: 0 auto;
  }


}



/* iPad 橫向模式優化 */
@media screen and (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  /* DesignGallerySection 右側小圖容器優化 */
  .design-gallery-section .flex.flex-col {
    /* 確保右側小圖在 iPad 橫向模式下也能垂直平均分佈 */
    justify-content: space-between !important;
    align-self: stretch !important;
  }

  /* 確保外層容器正確對齊 */
  .design-gallery-section .flex:not(.flex-col) {
    align-items: stretch !important;
  }
}

/* iPad Air 特定優化 - 確保間距一致 */
@media screen and (min-width: 820px) and (max-width: 834px) {
  .design-gallery-section {
    /* 統一 iPad Air 的 padding */
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  .design-gallery-section .flex.flex-col {
    justify-content: space-between !important;
    align-self: stretch !important;
  }

  .design-gallery-section .flex:not(.flex-col):not(.zoom-icon-overlay) {
    align-items: stretch !important;
  }
}

/* 手機裝置 DesignGallerySection 修復 */
@media screen and (max-width: 767px) {
  .design-gallery-section .flex.flex-col {
    /* 確保手機上右側小圖垂直平均分佈 */
    justify-content: space-between !important;
    align-self: stretch !important;
    /* 手機上的最小高度 */
    min-height: 200px !important;
  }

  .design-gallery-section .flex:not(.flex-col):not(.zoom-icon-overlay) {
    align-items: stretch !important;
  }
}

/* 桌面版本 DesignGallerySection 修復 (>1024px) */
@media screen and (min-width: 1025px) {
  .design-gallery-section .flex.flex-col {
    /* 確保桌面版右側小圖垂直平均分佈 */
    justify-content: space-between !important;
    align-self: stretch !important;
    /* 桌面版需要設定高度來與左側大圖對齊 */
    height: 100% !important;
  }

  .design-gallery-section .flex:not(.flex-col):not(.zoom-icon-overlay) {
    align-items: stretch !important;
  }
}

/* 錶匠體驗表單小螢幕修復 */
@media screen and (max-width: 767px) {
  /* 確保性別選項在手機上垂直排列且可見 */
  .movement-assembling-form [class*="space-y-2"][class*="md:space-y-0"][class*="md:flex"][class*="md:gap-6"] {
    flex-direction: column !important;
    gap: 0.75rem !important;
    width: 100% !important;
    max-width: none !important;
  }

  /* 確保性別選項標籤完全可見 */
  .movement-assembling-form [class*="space-y-2"][class*="md:space-y-0"][class*="md:flex"][class*="md:gap-6"] label {
    width: 100% !important;
    min-width: 0 !important;
    flex: none !important;
    white-space: normal !important;
    overflow: visible !important;
  }

  /* 場次選項在手機上確保完全可見 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="lg:grid-cols-2"] {
    grid-template-columns: 1fr !important;
    gap: 0.75rem !important;
  }

  /* 場次選項標籤在手機上的優化 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="lg:grid-cols-2"] label {
    min-height: 50px !important;
    padding: 0.75rem !important;
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
  }
}

/* 中等螢幕修復 */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  /* 確保性別選項在平板上有足夠空間 */
  .movement-assembling-form [class*="space-y-2"][class*="md:space-y-0"][class*="md:flex"][class*="md:gap-6"] {
    gap: 1rem !important;
    flex-wrap: wrap !important;
  }

  /* 場次選項在平板上保持 2 列但確保可見 */
  .movement-assembling-form .grid[class*="grid-cols-1"][class*="lg:grid-cols-2"] {
    gap: 1rem !important;
  }
}

/* 錶款和文章卡片的響應式優化 */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  /* 平板尺寸：確保兩欄佈局的間距和比例 */
  .watch-card-grid,
  .blog-card-grid {
    gap: 1.5rem !important;
  }

  /* 確保卡片在平板上有適當的最小寬度 */
  .watch-card,
  .blog-card {
    min-width: 280px;
  }
}

/* 確保所有卡片高度一致 */
.watch-card-grid,
.blog-card-grid {
  align-items: stretch;
}

.watch-card-grid > *,
.blog-card-grid > * {
  height: 100%;
}

/* iOS Safari 特定修復：DesignGallerySection 內容截斷問題 - 加強版 */
.ios-safari .design-gallery-section,
.ios-safari-detected .design-gallery-section {
  /* 確保區塊有足夠的高度 */
  min-height: auto !important;
  height: auto !important;
  /* 完全避免內容被截斷 */
  overflow: visible !important;
  overflow-x: visible !important;
  overflow-y: visible !important;
  /* 確保正確的渲染上下文 */
  contain: none !important;
  /* 禁用可能導致截斷的屬性 */
  clip: none !important;
  clip-path: none !important;
}

/* iOS Safari 特定修復：圖片容器和佈局 */
.ios-safari .design-gallery-section .flex,
.ios-safari-detected .design-gallery-section .flex {
  /* 確保 flex 容器正確計算 */
  min-width: 0 !important;
  flex-shrink: 0 !important;
  /* 防止圖片容器被壓縮 */
  width: auto !important;
  height: auto !important;
}

/* iOS Safari 特定修復：圖片元素 */
.ios-safari .design-gallery-section img,
.ios-safari-detected .design-gallery-section img {
  /* 確保圖片正確載入和顯示 */
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  /* 防止圖片變形 */
  object-fit: cover !important;
  /* 確保圖片可見 */
  opacity: 1 !important;
  visibility: visible !important;
}

.ios-safari .design-gallery-section .grid,
.ios-safari-detected .design-gallery-section .grid {
  /* 確保 grid 容器正確計算高度 */
  min-height: fit-content !important;
  height: auto !important;
  /* 避免 grid 項目被截斷 */
  align-items: start !important;
  /* 確保 grid 有足夠的空間 */
  grid-template-rows: auto !important;
}

.ios-safari .design-gallery-section h2,
.ios-safari-detected .design-gallery-section h2 {
  /* 確保標題在 iOS Safari 上正確定位 */
  position: static !important;
  -webkit-transform: none !important;
  transform: none !important;
  /* 修復可能的 margin 問題 */
  margin-bottom: 1.5rem !important;
  /* 確保標題不會被隱藏 */
  visibility: visible !important;
  opacity: 1 !important;
}

.ios-safari .design-gallery-section .space-y-6 > *,
.ios-safari-detected .design-gallery-section .space-y-6 > * {
  /* 確保文字內容區塊的間距正確 */
  margin-top: 0 !important;
  margin-bottom: 1.5rem !important;
  /* 確保內容可見 */
  visibility: visible !important;
  opacity: 1 !important;
}

.ios-safari .design-gallery-section .space-y-6 > *:last-child,
.ios-safari-detected .design-gallery-section .space-y-6 > *:last-child {
  margin-bottom: 0 !important;
}

.ios-safari .design-gallery-section .flex.gap-2,
.ios-safari .design-gallery-section .flex.gap-3,
.ios-safari .design-gallery-section .flex.gap-4,
.ios-safari-detected .design-gallery-section .flex.gap-2,
.ios-safari-detected .design-gallery-section .flex.gap-3,
.ios-safari-detected .design-gallery-section .flex.gap-4 {
  /* 確保圖片容器不會被截斷 */
  min-height: fit-content !important;
  height: auto !important;
  /* 確保圖片容器完全可見 */
  overflow: visible !important;
  /* 禁用可能導致圖片消失的屬性 */
  clip: none !important;
  clip-path: none !important;
  /* 確保足夠的寬度 */
  width: 100% !important;
  max-width: none !important;
}

/* iOS Safari 特定修復：水平排列的圖片容器 */
.ios-safari .design-gallery-section .flex:not(.flex-col),
.ios-safari-detected .design-gallery-section .flex:not(.flex-col) {
  /* 使用 stretch 確保右側小圖容器與左側大圖高度一致 */
  align-items: stretch !important;
}

/* 特別針對右側小圖容器的修復 */
.ios-safari .design-gallery-section .flex.flex-col,
.ios-safari-detected .design-gallery-section .flex.flex-col {
  /* 確保右側小圖容器不會消失 */
  flex-shrink: 0 !important;
  min-width: fit-content !important;
  width: auto !important;
  /* 確保容器可見 */
  visibility: visible !important;
  opacity: 1 !important;
  /* 避免被截斷 */
  overflow: visible !important;
  /* 確保垂直分佈正常工作 */
  justify-content: space-between !important;
  /* 移除固定高度，讓容器自然拉伸 */
  align-self: stretch !important;
}

/* 放大圖示覆蓋層修復 - 避免 design-gallery-section 樣式衝突 */
.zoom-icon-overlay {
  /* 確保放大圖示覆蓋層不受其他 flex 規則影響 */
  align-items: center !important;
  justify-content: center !important;
  /* 確保圓形背景正確顯示 */
  width: auto !important;
  height: auto !important;
  min-width: auto !important;
  min-height: auto !important;
  flex-shrink: initial !important;
  /* 防止背景被拉深 */
  align-self: center !important;
  flex: none !important;
}

/* 手錶詳情頁面圖片優化 */
.watch-detail-thumbnail {
  aspect-ratio: 1 !important;
  overflow: hidden !important;
}

.watch-detail-thumbnail img {
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
}

/* 確保縮圖容器與主圖片對齊 */
.watch-detail-thumbnails {
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

@media screen and (max-width: 768px) {
  /* 手機版：主圖片使用原始比例，不強制正方形 */
  .watch-detail-main-image {
    aspect-ratio: auto !important; /* 使用圖片原始比例 */
    margin-top: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 0.75rem !important; /* 手機版適中間距 - 12px */
  }

  /* 縮圖使用響應式正方形比例 */
  .watch-detail-thumbnail {
    aspect-ratio: 1 !important;
    min-height: 0 !important; /* 移除固定高度限制 */
  }

  /* 確保縮圖容器與主圖片對齊 */
  .watch-detail-thumbnails {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
    gap: 0.25rem !important; /* 手機版適中間距 - 4px */
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  /* 平板版：主圖片使用原始比例，避免強制比例造成的間距 */
  .watch-detail-main-image {
    aspect-ratio: auto !important; /* 使用圖片原始比例 */
    margin-top: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 0.75rem !important; /* 平板版適中間距 */
  }

  /* 縮圖使用響應式正方形比例 */
  .watch-detail-thumbnail {
    aspect-ratio: 1 !important;
    min-height: 0 !important; /* 移除固定高度限制 */
  }

  /* 確保縮圖容器與主圖片對齊 */
  .watch-detail-thumbnails {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
    gap: 0.375rem !important; /* 平板版中等間距 */
  }
}

@media screen and (min-width: 1025px) {
  /* 桌面版：主圖片使用原始比例，避免強制比例造成的間距 */
  .watch-detail-main-image {
    aspect-ratio: auto !important; /* 使用圖片原始比例 */
    margin-top: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 0.5rem !important; /* 桌面版適中間距 - 8px */
  }

  /* 縮圖使用正方形比例，縮小尺寸讓佈局更精緻 */
  .watch-detail-thumbnail {
    aspect-ratio: 1 !important;
    height: 90px !important;
    min-height: 90px !important;
    max-height: 90px !important;
  }

  /* 確保縮圖容器與主圖片對齊，增加列數讓縮圖更小 */
  .watch-detail-thumbnails {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
    gap: 0.375rem !important; /* 桌面版進一步縮小間距 - 6px */
    display: grid !important;
    grid-template-columns: repeat(6, 1fr) !important; /* 增加到6列，讓縮圖更小 */
    max-width: 100% !important;
    justify-items: stretch !important; /* 確保每個縮圖填滿格子 */
  }
}
