'use client';

import { useState, useCallback, useEffect } from 'react';
import WatchCard from '@/components/WatchCard';
import WatchCardSkeleton from '@/components/WatchCardSkeleton';
import { WatchesResponse } from '@/types/watch';
import { useLazyLoading } from '@/hooks/useLazyLoading';
import { Shield, Calendar, Search, Handshake } from 'lucide-react';
import { useAnimationCompatibility } from '@/hooks/useBrowserDetection';

// 由於這是 client component，我們需要在父組件或 layout 中設定 metadata

const PreOwnedPage = () => {
  const [brands, setBrands] = useState<string[]>([]);
  const [selectedBrand, setSelectedBrand] = useState<string>('all');

  // iOS 檢測和動畫兼容性
  useAnimationCompatibility();

  // 獲取手錶資料的函數
  const fetchWatchesData = useCallback(async (page: number, pageSize: number) => {
    const url = selectedBrand === 'all'
      ? `/api/pre-owned-watches?page=${page}&pageSize=${pageSize}`
      : `/api/pre-owned-watches?brand=${encodeURIComponent(selectedBrand)}&page=${page}&pageSize=${pageSize}`;

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('無法獲取手錶資料');
    }

    const data: WatchesResponse = await response.json();

    // 更新品牌列表（只在第一次載入時）
    if (page === 0) {
      setBrands(data.brands);
    }

    return {
      items: data.watches,
      total: data.total,
      hasMore: data.hasMore
    };
  }, [selectedBrand]);

  // 使用 lazy loading hook
  const {
    items: watches,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    isLoadingMore
  } = useLazyLoading({
    fetchFunction: fetchWatchesData,
    pageSize: 9,
    initialLoad: true
  });

  // 品牌篩選變更
  const handleBrandChange = (brand: string) => {
    setSelectedBrand(brand);
  };

  // 當品牌選擇改變時重新載入
  useEffect(() => {
    refresh();
  }, [selectedBrand, refresh]);

  if (error) {
    return (
      <div className="min-h-screen bg-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl font-light mb-4" style={{ color: '#2b354d' }}>載入失敗</h1>
          <p className="mb-8" style={{ color: '#2b354d' }}>{error}</p>
          <button
            onClick={refresh}
            className="px-6 py-3 rounded-lg transition-colors"
            style={{ backgroundColor: '#2b354d', color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
            onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
          >
            重新載入
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 頁面標題 */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4" style={{ color: '#2b354d' }}>典藏錶款</h1>
            <p className="text-lg" style={{ color: '#2b354d' }}>對任何錶款有興趣歡迎直接私訊粉專洽談</p>
          </div>
        </div>
      </div>

      {/* 品牌篩選器 */}
      <section className="py-8 bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-2">
            <button
              onClick={() => handleBrandChange('all')}
              className={`px-4 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                selectedBrand === 'all'
                  ? 'shadow-lg'
                  : 'bg-slate-100 hover:bg-slate-200'
              }`}
              style={{
                backgroundColor: selectedBrand === 'all' ? '#2b354d' : undefined,
                color: selectedBrand === 'all' ? '#ffffff' : '#2b354d'
              }}
            >
              全部品牌
            </button>
            {brands.map((brand) => (
              <button
                key={brand}
                onClick={() => handleBrandChange(brand)}
                className={`px-4 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                  selectedBrand === brand
                    ? 'shadow-lg'
                    : 'bg-slate-100 hover:bg-slate-200'
                }`}
                style={{
                  backgroundColor: selectedBrand === brand ? '#2b354d' : undefined,
                  color: selectedBrand === brand ? '#ffffff' : '#2b354d'
                }}
              >
                {brand}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* 手錶列表 */}
      <section className="py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading && watches.length === 0 ? (
            // 初始載入骨架屏
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 watch-card-grid">
              {Array.from({ length: 9 }).map((_, index) => (
                <WatchCardSkeleton key={index} />
              ))}
            </div>
          ) : watches.length === 0 ? (
            <div className="text-center py-20">
              <h3 className="text-2xl font-light mb-4" style={{ color: '#2b354d' }}>
                {selectedBrand === 'all' ? '暫無手錶資料' : `暫無 ${selectedBrand} 品牌的手錶`}
              </h3>
              <p style={{ color: '#2b354d' }}>請稍後再試或選擇其他品牌</p>
            </div>
          ) : (
            <>
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 watch-card-grid">
                {watches.map((watch) => (
                  <WatchCard key={watch.id} watch={watch} />
                ))}
              </div>

              {/* 載入更多內容的骨架屏 */}
              {isLoadingMore && (
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 watch-card-grid mt-8">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <WatchCardSkeleton key={`loading-${index}`} />
                  ))}
                </div>
              )}

              {/* 查看更多按鈕 */}
              {hasMore && !isLoadingMore && (
                <div className="flex items-center justify-center mt-12">
                  <button
                    onClick={loadMore}
                    className="px-8 py-3 rounded-lg border border-slate-300 transition-all duration-200 hover:border-slate-400 hover:shadow-md"
                    style={{
                      backgroundColor: '#ffffff',
                      color: '#2b354d',
                      fontWeight: '500'
                    }}
                  >
                    查看更多手錶
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Weaven 安心購錶承諾區塊 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 主標題 */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4" style={{ color: '#2b354d' }}>
              Weaven 安心購錶承諾
            </h2>
          </div>

          {/* 承諾內容 - 2x2 網格佈局 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {/* 承諾一：100% 正品保證 */}
            <div className="bg-white rounded-lg p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Shield className="w-8 h-8" style={{ color: '#f59e0b' }} />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3" style={{ color: '#2b354d' }}>
                    100% 正品保證
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    我們的資深錶匠團隊會對每一支腕錶的機芯與來源進行嚴格的交叉驗證，確保您收藏的每一件作品皆為原裝正品。
                  </p>
                </div>
              </div>
            </div>

            {/* 承諾二：一年機芯保固 */}
            <div className="bg-white rounded-lg p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Handshake className="w-8 h-8" style={{ color: '#f59e0b' }} />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3" style={{ color: '#2b354d' }}>
                    一年機芯保固
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    我們為售出的每一支腕錶提供自購買日起一年的非人為機芯故障保固。所有保固服務皆由我們的專業團隊為您處理。
                  </p>
                </div>
              </div>
            </div>

            {/* 承諾三：詳實錶況說明 */}
            <div className="bg-white rounded-lg p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Search className="w-8 h-8" style={{ color: '#f59e0b' }} />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3" style={{ color: '#2b354d' }}>
                    詳實錶況說明
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    手錶描述依原錶況如實說明，盡可能揭露所有使用痕跡，讓您在預約鑑賞前就充分掌握資訊。
                  </p>
                </div>
              </div>
            </div>

            {/* 承諾四：安全的預約鑑賞流程 */}
            <div className="bg-white rounded-lg p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Calendar className="w-8 h-8" style={{ color: '#f59e0b' }} />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3" style={{ color: '#2b354d' }}>
                    安全的預約鑑賞流程
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    您的預約將由專人一對一服務。我們確保整個鑑賞與交易過程安全、私密且無壓力，您可以專注於尋找心儀的腕錶。
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 行動呼籲連結 */}
          <div className="text-center">
            <a
              href="/support/collection-warranty"
              className="inline-flex items-center text-lg font-medium transition-colors duration-200 hover:opacity-80"
              style={{ color: '#2b354d' }}
            >
              閱讀完整的典藏錶款保固政策
              <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PreOwnedPage;
